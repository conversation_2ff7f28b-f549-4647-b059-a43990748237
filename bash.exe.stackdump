Stack trace:
Frame         Function      Args
0007FFFFA0F0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFA0F0, 0007FFFF8FF0) msys-2.0.dll+0x1FEBA
0007FFFFA0F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3C8) msys-2.0.dll+0x67F9
0007FFFFA0F0  000210046832 (000210285FF9, 0007FFFF9FA8, 0007FFFFA0F0, 000000000000) msys-2.0.dll+0x6832
0007FFFFA0F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA0F0  0002100690B4 (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA3D0  00021006A49D (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF1F440000 ntdll.dll
7FFF1D450000 KERNEL32.DLL
7FFF1C630000 KERNELBASE.dll
7FFF190F0000 apphelp.dll
7FFF1F120000 USER32.dll
7FFF1CFB0000 win32u.dll
000210040000 msys-2.0.dll
7FFF1D270000 GDI32.dll
7FFF1CCF0000 gdi32full.dll
7FFF1CFE0000 msvcp_win.dll
7FFF1CBA0000 ucrtbase.dll
7FFF1E770000 advapi32.dll
7FFF1E4D0000 msvcrt.dll
7FFF1EA20000 sechost.dll
7FFF1EBC0000 RPCRT4.dll
7FFF1BB90000 CRYPTBASE.DLL
7FFF1C590000 bcryptPrimitives.dll
7FFF1E730000 IMM32.DLL
