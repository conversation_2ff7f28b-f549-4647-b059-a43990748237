export const GIFT_SVG = `<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.83334 9.16669V16.6667C3.83334 17.5872 4.57954 18.3334 5.50001 18.3334H15.5C16.4205 18.3334 17.1667 17.5872 17.1667 16.6667V9.16669" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.5 4.79169V5.83335H7.58333C6.43274 5.83335 5.5 4.90061 5.5 3.75002V3.54169C5.5 2.50615 6.33947 1.66669 7.375 1.66669C9.10092 1.66669 10.5 3.0658 10.5 4.79169Z" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.5 4.79169V5.83335H13.4167C14.5672 5.83335 15.5 4.90061 15.5 3.75002V3.54169C15.5 2.50615 14.6605 1.66669 13.625 1.66669C11.8991 1.66669 10.5 3.0658 10.5 4.79169Z" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.5369 5.83331H3.46755C2.98255 5.83331 2.58903 6.20512 2.5878 6.66454L2.58335 8.3312C2.58212 8.79223 2.97635 9.16665 3.4631 9.16665H17.5369C18.0228 9.16665 18.4167 8.79356 18.4167 8.33331V6.66665C18.4167 6.20641 18.0228 5.83331 17.5369 5.83331Z" stroke="white" stroke-width="1.25" stroke-linejoin="round"/>
<path d="M10.5 9.16669V18.3334" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const USER_ADD_SVG = `<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13 15.8333C13 13.9916 10.7617 12.5 8 12.5C5.23833 12.5 3 13.9916 3 15.8333M16.3333 13.3333V10.8333M16.3333 10.8333V8.33331M16.3333 10.8333H13.8333M16.3333 10.8333H18.8333M8 9.99998C7.11594 9.99998 6.2681 9.64879 5.64298 9.02367C5.01786 8.39855 4.66667 7.5507 4.66667 6.66665C4.66667 5.78259 5.01786 4.93474 5.64298 4.30962C6.2681 3.6845 7.11594 3.33331 8 3.33331C8.88405 3.33331 9.7319 3.6845 10.357 4.30962C10.9821 4.93474 11.3333 5.78259 11.3333 6.66665C11.3333 7.5507 10.9821 8.39855 10.357 9.02367C9.7319 9.64879 8.88405 9.99998 8 9.99998Z" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const NOTIFICATION_SVG = `<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_13021_2631)">
<path d="M15.3082 14.7252C16.2498 14.7252 17.0165 13.9752 17.0248 13.0502C17.0248 12.5835 16.8332 12.1335 16.4915 11.8002L16.0332 11.3501C15.5998 10.9251 15.3582 10.3585 15.3582 9.75847V7.50015C15.3582 6.81681 15.2498 6.13349 14.9748 5.50015C14.1582 3.61682 12.3082 2.48347 10.3499 2.49181C7.59152 2.49181 5.35818 4.68347 5.35818 7.38347V9.75016C5.35818 10.3502 5.11652 10.9168 4.68318 11.3418L4.22485 11.7918C3.88318 12.1251 3.69151 12.5751 3.69151 13.0418C3.69151 13.9668 4.45818 14.7168 5.40818 14.7168H15.3165L15.3082 14.7252Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.04959 17.5001H11.6579" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_13021_2631">
<rect width="20" height="20" fill="white" transform="translate(0.5)"/>
</clipPath>
</defs>
</svg>`;

// Navigation Icons
export const homeSvg = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.85354 8.61072L11.3175 3.54689C12.2483 2.8177 13.5517 2.8177 14.4825 3.54689L20.9465 8.61072C21.6843 9.18877 21.8893 10.0491 21.8996 11.0435C21.9001 11.0974 21.8984 11.1495 21.8943 11.2033C21.8525 11.7548 21.5634 15.1249 20.4949 18.9084C20.1173 19.9615 19.2289 21 18.0067 21H16.818C16.0777 21 15.3713 20.3956 15.3713 19.65L15.4776 16.846C15.4776 15.4122 14.3236 14.2498 12.9 14.2498C11.4764 14.2498 10.3224 15.4122 10.3224 16.846L10.4417 19.65C10.4417 20.3956 9.72229 21 8.98203 21H7.79335C6.5711 21 5.6827 19.9615 5.30511 18.9084C4.23663 15.1249 3.9475 11.7548 3.90568 11.2033C3.9016 11.1495 3.89986 11.0974 3.90041 11.0435C3.91068 10.0491 4.11566 9.18877 4.85354 8.61072Z" fill="currentColor"/>
</svg>`;

export const dealsSvg = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.1694 1.46967C12.31 1.32902 12.5008 1.25 12.6997 1.25H22.6997C23.1139 1.25 23.4497 1.58579 23.4497 2V12C23.4497 12.1989 23.3707 12.3897 23.23 12.5303L14.6442 21.1161C13.5703 22.1901 11.8291 22.1901 10.7552 21.1161L3.58358 13.9445C2.50964 12.8706 2.50964 11.1294 3.58358 10.0555L12.1694 1.46967ZM19.7 6.5C19.7 5.67157 19.0285 5 18.2 5C17.3716 5 16.7 5.67157 16.7 6.5C16.7 7.32843 17.3716 8 18.2 8C19.0285 8 19.7 7.32843 19.7 6.5ZM13.0021 10.3918C13.1802 10.2169 13.6792 10.1346 14.126 10.5736C14.3246 10.7688 14.4238 10.9956 14.4455 11.1904C14.4915 11.6021 14.8625 11.8985 15.2741 11.8526C15.6858 11.8066 15.9822 11.4357 15.9363 11.024C15.9013 10.7106 15.8015 10.4016 15.6434 10.1156L15.7258 10.0347C16.0212 9.74437 16.0254 9.26951 15.735 8.97407C15.4447 8.67863 14.9699 8.67448 14.6744 8.96481L14.5764 9.06109C13.7412 8.60967 12.6804 8.60493 11.9508 9.32196C11.567 9.69912 11.285 10.2089 11.3686 10.8405C11.4445 11.4143 11.8059 11.9258 12.2779 12.3897C12.6693 12.7743 12.7778 13.0167 12.7963 13.1407C12.8053 13.2017 12.812 13.3252 12.5702 13.5628C12.383 13.7467 12.2344 13.7668 12.0895 13.7403C11.8987 13.7054 11.6353 13.5667 11.339 13.2756C11.1001 13.0408 10.9755 12.7751 10.9413 12.5541C10.8779 12.1448 10.4947 11.8643 10.0854 11.9277C9.67601 11.9911 9.39556 12.3743 9.45894 12.7836C9.51018 13.1146 9.63093 13.4409 9.81193 13.7431L9.73946 13.8143C9.44402 14.1046 9.43988 14.5795 9.7302 14.8749C10.0205 15.1704 10.4954 15.1745 10.7908 14.8842L10.8617 14.8145C11.1519 15.0062 11.4736 15.1526 11.8199 15.2159C12.4607 15.3329 13.0979 15.1473 13.6215 14.6327C14.0905 14.1718 14.3802 13.5955 14.28 12.9204C14.1891 12.3084 13.7961 11.7786 13.3293 11.3198C12.943 10.9403 12.8661 10.7229 12.8556 10.6437L12.8553 10.6413C12.8518 10.6175 12.842 10.5492 13.0021 10.3918Z" fill="currentColor"/>
</svg>`;

export const sneakersSvg = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_15005_26)">
<path d="M6.7526 12.4836L8.88906 13.1816C9.41275 13.3526 9.89916 13.3018 10.3761 13.0264L10.7311 12.8215C11.3 12.4931 11.961 12.4776 12.5446 12.7789L13.5587 13.3026C14.2598 13.6646 14.9244 13.826 15.7135 13.826H24.4525C24.4806 13.6212 24.4965 13.4135 24.4997 13.2038C24.5134 12.3109 24.0323 11.5357 23.244 11.1806L18.9157 9.23245L18.4838 10.031C18.3566 10.2664 18.1145 10.4 17.8645 10.4C17.7515 10.4 17.6369 10.3727 17.5305 10.3151C17.1888 10.1304 17.0616 9.70357 17.2464 9.36187L17.6294 8.65352L16.555 8.16992L16.0258 9.14593C15.8984 9.38091 15.6565 9.51422 15.4068 9.51422C15.2936 9.51422 15.1787 9.48684 15.0722 9.42906C14.7307 9.24393 14.6039 8.81698 14.7891 8.47547L15.2687 7.59099L13.9777 7.00995L13.5646 7.78259C13.4379 8.01958 13.1949 8.15449 12.9437 8.15449C12.8318 8.15449 12.7183 8.12771 12.6127 8.07126C12.2701 7.88814 12.1409 7.46194 12.324 7.11935L12.692 6.43125L11.1358 5.73087C11.0451 5.68998 10.9467 5.66888 10.8471 5.66888C10.0152 5.66888 9.31671 6.31885 9.25688 7.14865C9.2067 7.84397 8.62121 8.38862 7.92392 8.38862C7.18701 8.38862 6.5875 7.78911 6.5875 7.0522V6.74754C6.5875 6.45967 6.47519 6.18887 6.27135 5.98498C6.06742 5.781 5.79647 5.66691 5.50879 5.66888C3.93162 5.66902 2.64846 6.95222 2.64846 8.52944V11.7341C4.06765 11.7911 5.3593 12.0284 6.7526 12.4836Z" fill="currentColor"/>
<path d="M15.7135 15.2328C14.7019 15.2328 13.8121 15.0167 12.9133 14.5525L11.8992 14.0289C11.7408 13.9472 11.5888 13.9507 11.4345 14.0398L11.0795 14.2447C10.2602 14.7178 9.35165 14.8126 8.45221 14.5188L6.31576 13.8208C4.82675 13.3343 3.523 13.1267 1.95643 13.1267H1.8618L0.806308 14.9549C0.398627 15.661 0.397877 16.5029 0.804338 17.2068C1.21075 17.9108 1.94021 18.3311 2.75557 18.3311H9.35427C9.54339 18.3311 9.72453 18.255 9.85682 18.1198L10.6974 17.2614C10.7392 17.2187 10.7718 17.205 10.8315 17.205H11.9332C12.0349 17.205 12.1209 17.2909 12.1209 17.3927V17.5325C12.1209 17.921 12.4358 18.2359 12.8242 18.2359H19.3891C20.7373 18.2359 22.0107 17.7126 22.9747 16.7623C23.429 16.3144 23.7895 15.7964 24.0458 15.2328H15.7135V15.2328Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_15005_26">
<rect width="24" height="24" fill="white" transform="translate(0.500008)"/>
</clipPath>
</defs>
</svg>`;

export const cashbackSvg = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.08895 3.38546C8.1778 3.52092 8.07749 3.75186 7.87687 4.21374C7.79966 4.39151 7.7296 4.57312 7.66706 4.75819C7.59288 4.9777 7.55579 5.08746 7.47739 5.14373C7.39898 5.19999 7.29013 5.19999 7.07243 5.19999H4.48139C3.94165 5.19999 3.50411 5.63651 3.50411 6.17499C3.50411 6.71346 3.94165 7.14998 4.48139 7.14998H16.2776C17.1093 7.13732 18.9295 7.14275 19.5563 7.26578C20.4631 7.3874 21.2639 7.65257 21.9051 8.29227C22.5463 8.93197 22.812 9.73091 22.9339 10.6355C23.0501 11.4972 23.05 12.5859 23.05 13.9062V15.9937C23.05 17.314 23.0501 18.4027 22.9339 19.2644C22.812 20.169 22.5463 20.9679 21.9051 21.6076C21.2639 22.2473 20.4631 22.5125 19.5563 22.6341C18.6927 22.75 17.6014 22.75 16.278 22.7499H10.274C8.49197 22.7499 7.0556 22.75 5.92591 22.5984C4.75304 22.4411 3.76551 22.1046 2.9812 21.3221C2.19688 20.5396 1.85953 19.5543 1.70185 18.3842C1.54997 17.2572 1.54999 15.8241 1.55001 14.0463V6.175C1.55001 4.55957 2.86263 3.25001 4.48183 3.25001L7.29625 3.25C7.76548 3.25 8.0001 3.25 8.08895 3.38546ZM17.8 12.9998C18.9046 12.9998 19.8 13.8952 19.8 14.9998C19.8 16.1043 18.9046 16.9998 17.8 16.9998C16.6954 16.9998 15.8 16.1043 15.8 14.9998C15.8 13.8952 16.6954 12.9998 17.8 12.9998Z" fill="currentColor"/>
<path d="M19.7557 6.03151C19.863 6.0463 19.9555 5.95355 19.9339 5.84742C19.4001 3.22413 17.0803 1.25 14.2994 1.25C11.6264 1.25 9.37933 3.07399 8.73504 5.54525C8.68747 5.72772 8.82963 5.90006 9.01821 5.90006H16.2679C16.6968 5.89365 17.3712 5.89192 18.0232 5.90742C18.6197 5.92161 19.3159 5.95164 19.7557 6.03151Z" fill="currentColor"/>
</svg>`;

export const profileSvg = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.35001 21C3.35001 16.7198 6.8198 13.25 11.1 13.25H13.1C17.3802 13.25 20.85 16.7198 20.85 21C20.85 21.4142 20.5142 21.75 20.1 21.75H4.10001C3.6858 21.75 3.35001 21.4142 3.35001 21Z" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.35001 7C7.35001 4.37665 9.47666 2.25 12.1 2.25C14.7234 2.25 16.85 4.37665 16.85 7C16.85 9.62335 14.7234 11.75 12.1 11.75C9.47666 11.75 7.35001 9.62335 7.35001 7Z" fill="currentColor"/>
</svg>`;
